<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متابعة تفعيل الهيكل التنظيمي - نظام تقييم الهياكل التنظيمية</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f0f2f5;
        }
        .evaluation-header {
            background: linear-gradient(to right, #004285, #0059b3);
            color: white;
            padding: 2rem;
            border-radius: 12px 12px 0 0;
        }
        .evaluation-header h1 {
            font-size: 2.25rem; /* 36px */
            font-weight: 700;
        }
        .evaluation-header p {
            font-size: 1.125rem; /* 18px */
            opacity: 0.9;
        }
        .progress-track {
            background-color: #e2e8f0;
            border-radius: 50rem;
            padding: 0.25rem;
        }
        .progress-fill {
            background-color: #48bb78; /* أخضر للتقدم */
            border-radius: 50rem;
            transition: width 0.5s ease-in-out;
        }
        .axis-card {
            background-color: #ffffff;
            border-radius: 12px;
            box-shadow: 0 6px 12px rgba(0,0,0,0.07);
            margin-bottom: 1.5rem;
            padding: 1.75rem;
        }
        .axis-card h3 {
            color: #0059b3;
            font-size: 1.5rem; /* 24px */
            font-weight: 700;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 0.75rem;
        }
        .axis-item {
            display: grid;
            grid-template-columns: repeat(12, 1fr); /* 12-column grid */
            gap: 1rem;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .axis-item:last-child {
            border-bottom: none;
        }
        .axis-item label.axis-name {
            grid-column: span 5 / span 5; /* يأخذ 5 أعمدة */
            font-weight: 500;
            color: #2d3748;
        }
        .axis-item .weight-tag {
            grid-column: span 2 / span 2; /* يأخذ عمودين */
            background-color: #e2e8f0;
            color: #4a5568;
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 700;
            text-align: center;
        }
        .axis-item .file-upload-container {
            grid-column: span 3 / span 3; /* يأخذ 3 أعمدة */
        }
        .axis-item .status-select-container {
            grid-column: span 2 / span 2; /* يأخذ عمودين */
        }
        .file-input-styled {
            position: relative;
            display: inline-block;
        }
        .file-input-styled input[type="file"] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        .file-input-label {
            background-color: #38a169; /* أخضر */
            color: white;
            padding: 0.6rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: inline-flex;
            align-items: center;
        }
        .file-input-label:hover {
            background-color: #2f855a;
        }
        .file-input-label i {
            margin-left: 0.5rem;
        }
        .file-name-display {
            font-size: 0.8rem;
            color: #718096;
            margin-top: 0.25rem;
        }
        .form-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #cbd5e0;
            border-radius: 8px;
            font-weight: 500;
        }
        .final-evaluation-card {
            background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
        }
        .final-evaluation-card .score-display {
            font-size: 3.5rem; /* 56px */
            font-weight: 700;
            color: #0059b3;
            margin-bottom: 0.5rem;
        }
        .final-evaluation-card .status-text {
            font-size: 1.75rem; /* 28px */
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .status-activated { color: #48bb78; } /* أخضر */
        .status-partially-activated { color: #ecc94b; } /* أصفر */
        .status-not-activated { color: #f56565; } /* أحمر */

    </style>
</head>
<body class="text-gray-800">
    <div class="container mx-auto my-8 p-0 md:p-4">
        <div class="evaluation-header">
            <h1>متابعة تفعيل: (استحداث قسم العمليات)</h1>
            <p>قرار رقم (<span class="font-bold">255</span>) بتاريخ <span class="font-bold">01-01-2025</span> - تنتهي فترة المتابعة في <span class="font-bold">30-06-2025</span></p>
        </div>

        <div class="bg-white p-6 rounded-b-xl shadow-xl">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-lg font-semibold text-gray-700">التقدم الإجمالي للتقييم:</span>
                    <span class="text-xl font-bold text-0059b3">65%</span>
                </div>
                <div class="progress-track">
                    <div class="progress-fill h-4" style="width: 65%;"></div>
                </div>
            </div>

            <!-- قسم محاور التفعيل -->
            <div class="axis-card">
                <h3><i class="fas fa-cogs ml-3 text-xl"></i>محاور التفعيل (الوزن النسبي: 30%)</h3>
                <div class="axis-item">
                    <label class="axis-name">التسكين (إصدار قرارات التعيين)</label>
                    <span class="weight-tag">20%</span>
                    <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_taskin" onchange="updateFileName('file_taskin', 'file_name_taskin')">
                            <label for="file_taskin" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_taskin" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                    <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed">لم يكتمل</option>
                            <option value="completed" selected>مكتمل</option>
                            <option value="partially_completed">مكتمل جزئياً</option>
                        </select>
                    </div>
                </div>
                 <div class="axis-item">
                    <label class="axis-name">توزيع الاختصاصات (على المعنيين)</label>
                    <span class="weight-tag">5%</span>
                     <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_ekhtisasat" onchange="updateFileName('file_ekhtisasat', 'file_name_ekhtisasat')">
                            <label for="file_ekhtisasat" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_ekhtisasat" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                    <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed">لم يكتمل</option>
                            <option value="completed" selected>مكتمل</option>
                        </select>
                    </div>
                </div>
                <div class="axis-item">
                    <label class="axis-name">الخطة التشغيلية (ووضع مؤشرات الأداء)</label>
                    <span class="weight-tag">5%</span>
                    <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_plan" onchange="updateFileName('file_plan', 'file_name_plan')">
                            <label for="file_plan" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_plan" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                    <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed" selected>لم يكتمل</option>
                            <option value="completed">مكتمل</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- قسم محاور الممارسة -->
            <div class="axis-card">
                <h3><i class="fas fa-chart-line ml-3 text-xl"></i>محاور الممارسة (الوزن النسبي: 70%)</h3>
                 <div class="axis-item">
                    <label class="axis-name">موائمة دليل الإجراءات بالاختصاصات (واعتماده)</label>
                    <span class="weight-tag">20%</span>
                     <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_daleel" onchange="updateFileName('file_daleel', 'file_name_daleel')">
                            <label for="file_daleel" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_daleel" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                     <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed" selected>لم يكتمل</option>
                            <option value="completed">مكتمل</option>
                        </select>
                    </div>
                </div>
                <div class="axis-item">
                    <label class="axis-name">متابعة تنفيذ إجراءات الجودة/العمل (وفاعليتها)</label>
                    <span class="weight-tag">10%</span>
                    <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_followup" onchange="updateFileName('file_followup', 'file_name_followup')">
                            <label for="file_followup" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_followup" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                    <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed">لم يكتمل</option>
                            <option value="completed" selected>مكتمل</option>
                        </select>
                    </div>
                </div>
                <div class="axis-item">
                    <label class="axis-name">القيمة المضافة ونتائج المؤشرات التشغيلية (والإحصائيات)</label>
                    <span class="weight-tag">40%</span>
                     <div class="file-upload-container">
                        <div class="file-input-styled">
                            <input type="file" id="file_kpi" onchange="updateFileName('file_kpi', 'file_name_kpi')">
                            <label for="file_kpi" class="file-input-label"><i class="fas fa-upload"></i> رفع الدليل</label>
                        </div>
                        <div id="file_name_kpi" class="file-name-display">لم يتم اختيار ملف</div>
                    </div>
                     <div class="status-select-container">
                        <select class="form-select">
                            <option value="not_completed">لم يكتمل</option>
                            <option value="completed" selected>مكتمل</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- النتيجة النهائية للتقييم -->
            <div class="final-evaluation-card mt-8">
                <h3 class="text-2xl font-bold text-gray-700 mb-3">النتيجة النهائية للتقييم</h3>
                <div class="score-display">65%</div>
                <div class="status-text status-partially-activated">مُفعَّلة جزئيًا</div>
                <p class="text-gray-600 text-lg mb-6">بناءً على الأدلة المقدمة، تم تقييم الوحدة التنظيمية بأنها مفعلة جزئياً. سيتم منح الوحدة مهلة إضافية (3 أشهر) لاستكمال المتطلبات المتبقية.</p>
                <div class="form-group">
                    <label for="eval_notes" class="text-lg font-semibold text-gray-700 mb-2 block">ملاحظات وتوصيات فريق التقييم:</label>
                    <textarea id="eval_notes" class="w-full p-3 border border-gray-300 rounded-lg min-h-[100px]" placeholder="أضف ملاحظاتك وتوصياتك هنا..."></textarea>
                </div>
                <div class="mt-8 flex justify-center space-x-reverse space-x-4">
                    <button class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-8 rounded-lg transition duration-300">
                        <i class="fas fa-save ml-2"></i> حفظ التقييم كمسودة
                    </button>
                    <button class="bg-0059b3 hover:bg-004285 text-white font-bold py-3 px-8 rounded-lg transition duration-300">
                        <i class="fas fa-check-double ml-2"></i> اعتماد وإرسال التقييم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateFileName(inputId, displayId) {
            const input = document.getElementById(inputId);
            const display = document.getElementById(displayId);
            if (input.files.length > 0) {
                display.textContent = input.files[0].name;
            } else {
                display.textContent = 'لم يتم اختيار ملف';
            }
        }
    </script>
</body>
</html>
